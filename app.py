import streamlit as st
import pandas as pd
import tempfile
import os
from src.extractor import BulletinExtractor
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration de la page Streamlit
st.set_page_config(
    page_title="Extracteur de Bulletins Scolaires",
    page_icon="📚",
    layout="wide"
)

# Titre de l'application
st.title("📚 Extracteur de Bulletins Scolaires")

# Description
st.markdown("""
Cette application permet d'extraire automatiquement les informations des bulletins scolaires
et de les exporter au format CSV. Vous pouvez télécharger plusieurs fichiers à la fois.
""")

# Initialisation de l'extracteur
@st.cache_resource
def get_extractor():
    return BulletinExtractor()

extractor = get_extractor()

# Zone de téléchargement des fichiers
uploaded_files = st.file_uploader(
    "Téléchargez vos bulletins (PNG, JPEG, PDF)",
    type=['png', 'jpg', 'jpeg', 'pdf'],
    accept_multiple_files=True
)

if uploaded_files:
    # Création d'un dossier temporaire pour les fichiers
    with tempfile.TemporaryDirectory() as temp_dir:
        file_paths = []
        
        # Sauvegarde des fichiers téléchargés
        for uploaded_file in uploaded_files:
            file_path = os.path.join(temp_dir, uploaded_file.name)
            with open(file_path, 'wb') as f:
                f.write(uploaded_file.getvalue())
            file_paths.append(file_path)
        
        try:
            # Traitement des fichiers
            with st.spinner('Extraction des données en cours...'):
                df = extractor.process_multiple_files(file_paths)
            
            # Affichage des données extraites
            st.subheader("Données extraites")
            st.dataframe(df)
            
            # Bouton d'export CSV
            csv = df.to_csv(index=False)
            st.download_button(
                label="Télécharger le CSV",
                data=csv,
                file_name="bulletins_extraits.csv",
                mime="text/csv"
            )
            
        except Exception as e:
            st.error(f"Une erreur est survenue lors du traitement : {str(e)}")
            logger.error(f"Erreur de traitement : {str(e)}")

# Pied de page
st.markdown("---")
st.markdown("""
<div style='text-align: center'>
    <p>Développé avec ❤️ pour faciliter l'analyse des bulletins scolaires</p>
</div>
""", unsafe_allow_html=True) 