import cv2
import numpy as np
import re
from typing import Dict, List, Tuple, Optional
from paddleocr import PaddleOCR

# Initialisation de PaddleOCR
ocr = PaddleOCR(use_angle_cls=True, lang='fr')

def detect_table_lines(image: np.ndarray) -> Tuple[List[np.ndarray], List[np.ndarray]]:
    """
    Détecte les lignes horizontales et verticales du tableau.
    Retourne les lignes horizontales et verticales.
    """
    # Conversion en niveaux de gris
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Binarisation
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    
    # Détection des lignes horizontales
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
    horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel)
    
    # Détection des lignes verticales
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
    vertical_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)
    
    return horizontal_lines, vertical_lines

def find_cell_boundaries(horizontal_lines: np.ndarray, vertical_lines: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """
    Trouve les coordonnées des cellules du tableau.
    Retourne une liste de tuples (x1, y1, x2, y2) pour chaque cellule.
    """
    # Combinaison des lignes horizontales et verticales
    table_lines = cv2.add(horizontal_lines, vertical_lines)
    
    # Trouver les contours
    contours, _ = cv2.findContours(table_lines, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filtrer les contours pour ne garder que les cellules
    cells = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        if w > 50 and h > 20:  # Filtrer les petites zones
            cells.append((x, y, x + w, y + h))
    
    return cells

def extract_text_from_cell(image: np.ndarray, cell_coords: Tuple[int, int, int, int]) -> str:
    """
    Extrait le texte d'une cellule du tableau en utilisant PaddleOCR.
    """
    x1, y1, x2, y2 = cell_coords
    cell = image[y1:y2, x1:x2]
    
    # Prétraitement de la cellule
    gray = cv2.cvtColor(cell, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    
    # Utilisation de PaddleOCR pour extraire le texte
    result = ocr.ocr(binary, cls=True)
    
    # Extraction du texte
    text = ""
    if result:
        for line in result[0]:
            if line[1][0]:  # Si le texte est détecté avec une confiance > 0
                text += line[1][0] + "\n"
    
    return text.strip()

def parse_header_text(text: str) -> Dict[str, str]:
    """
    Parse le texte de l'en-tête pour extraire les informations de l'élève.
    """
    info = {
        'nom': '',
        'prenom': '',
        'date_naissance': '',
        'classe': '',
        'professeur_principal': ''
    }
    
    # Patterns pour chaque champ
    patterns = {
        'nom': r'Nom\s*:\s*([^\n]+)',
        'prenom': r'Prénom\s*:\s*([^\n]+)',
        'date_naissance': r'Date de naissance\s*:\s*(\d{2}/\d{2}/\d{4})',
        'classe': r'Classe\s*:\s*([^\n]+)',
        'professeur_principal': r'Professeur principal\s*:\s*([^\n]+)'
    }
    
    # Extraction des informations
    for field, pattern in patterns.items():
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            info[field] = match.group(1).strip()
    
    return info

def parse_subject_info(text: str) -> Dict[str, str]:
    """
    Parse le texte d'une matière pour extraire les notes et l'appréciation.
    """
    info = {
        'matiere': '',
        'note_t1': '',
        'note_t2': '',
        'note_t3': '',
        'moyenne': '',
        'position': '',
        'appreciation': '',
        'pastille': ''
    }
    
    # Extraction du nom de la matière
    lines = text.split('\n')
    if lines:
        info['matiere'] = lines[0].strip()
    
    # Patterns pour les notes et la position
    note_pattern = r'T(\d+)\s*:\s*(\d+[.,]\d+)'
    position_pattern = r'Position\s*:\s*(\d+)/(\d+)'
    moyenne_pattern = r'Moyenne\s*:\s*(\d+[.,]\d+)'
    
    # Extraction des notes
    for match in re.finditer(note_pattern, text):
        trimestre = int(match.group(1))
        note = match.group(2).replace(',', '.')
        if 1 <= trimestre <= 3:
            info[f'note_t{trimestre}'] = note
    
    # Extraction de la position
    position_match = re.search(position_pattern, text)
    if position_match:
        info['position'] = f"{position_match.group(1)}/{position_match.group(2)}"
    
    # Extraction de la moyenne
    moyenne_match = re.search(moyenne_pattern, text)
    if moyenne_match:
        info['moyenne'] = moyenne_match.group(1).replace(',', '.')
    
    # Extraction de l'appréciation
    # L'appréciation est généralement le dernier bloc de texte
    appreciation_lines = []
    in_appreciation = False
    
    for line in lines:
        if re.search(r'Appréciation|Commentaire', line, re.IGNORECASE):
            in_appreciation = True
            continue
        if in_appreciation and line.strip():
            appreciation_lines.append(line.strip())
    
    if appreciation_lines:
        info['appreciation'] = ' '.join(appreciation_lines)
    
    return info

def parse_footer_text(text: str) -> Dict[str, str]:
    """
    Parse le texte du pied de page pour extraire les informations générales.
    """
    info = {
        'absences': '',
        'retards': '',
        'moyenne_generale': ''
    }
    
    # Patterns pour chaque champ
    patterns = {
        'absences': r'Absences\s*:\s*(\d+)',
        'retards': r'Retards\s*:\s*(\d+)',
        'moyenne_generale': r'Moyenne générale\s*:\s*(\d+[.,]\d+)'
    }
    
    # Extraction des informations
    for field, pattern in patterns.items():
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            value = match.group(1)
            if field == 'moyenne_generale':
                value = value.replace(',', '.')
            info[field] = value
    
    return info

def detect_color_pastille(image: np.ndarray, cell_coords: Tuple[int, int, int, int]) -> str:
    """
    Détecte la couleur de la pastille dans une cellule.
    """
    x1, y1, x2, y2 = cell_coords
    cell = image[y1:y2, x1:x2]
    
    # Conversion en HSV pour une meilleure détection des couleurs
    hsv = cv2.cvtColor(cell, cv2.COLOR_BGR2HSV)
    
    # Définition des plages de couleurs pour les pastilles
    color_ranges = {
        'rouge': ([0, 100, 100], [10, 255, 255]),
        'orange': ([10, 100, 100], [25, 255, 255]),
        'jaune': ([25, 100, 100], [35, 255, 255]),
        'vert': ([35, 100, 100], [85, 255, 255]),
        'bleu': ([85, 100, 100], [130, 255, 255])
    }
    
    # Détection de la couleur dominante
    for color, (lower, upper) in color_ranges.items():
        mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
        if np.sum(mask) > 100:  # Seuil à ajuster
            return color
    
    return "Non détecté" 