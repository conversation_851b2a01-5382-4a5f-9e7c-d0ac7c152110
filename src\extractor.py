import os
import cv2
import numpy as np
import pandas as pd
from paddleocr import PaddleOCR
from pdf2image import convert_from_path
from PIL import Image
import logging
from .utils import (
    detect_table_lines,
    find_cell_boundaries,
    extract_text_from_cell,
    parse_header_text,
    parse_subject_info,
    parse_footer_text,
    detect_color_pastille
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BulletinExtractor:
    def __init__(self):
        """Initialise l'extracteur avec PaddleOCR."""
        self.ocr = PaddleOCR(use_angle_cls=True, lang='fr')
        
    def _preprocess_image(self, image):
        """Prétraite l'image pour améliorer la reconnaissance OCR."""
        # Conversion en niveaux de gris
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # Amélioration du contraste
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        # Réduction du bruit
        denoised = cv2.fastNlMeansDenoising(enhanced)
        return denoised

    def _extract_header_info(self, image):
        """Extrait les informations de l'en-tête du bulletin."""
        # Découpage de la zone d'en-tête
        header_height = int(image.shape[0] * 0.2)
        header = image[:header_height, :]
        
        # Détection des lignes du tableau
        horizontal_lines, vertical_lines = detect_table_lines(header)
        
        # Trouver les cellules
        cells = find_cell_boundaries(horizontal_lines, vertical_lines)
        
        # Extraire le texte de chaque cellule
        header_text = ""
        for cell in cells:
            text = extract_text_from_cell(header, cell)
            header_text += text + "\n"
        
        # Parser les informations
        return parse_header_text(header_text)

    def _extract_subject_info(self, image):
        """Extrait les informations des matières et leurs notes."""
        # Découpage de la zone des matières
        header_height = int(image.shape[0] * 0.2)
        footer_height = int(image.shape[0] * 0.1)
        subjects_area = image[header_height:-footer_height, :]
        
        # Détection des lignes du tableau
        horizontal_lines, vertical_lines = detect_table_lines(subjects_area)
        
        # Trouver les cellules
        cells = find_cell_boundaries(horizontal_lines, vertical_lines)
        
        subjects_info = []
        current_subject = None
        
        # Parcourir les cellules pour extraire les informations
        for cell in cells:
            text = extract_text_from_cell(subjects_area, cell)
            pastille = detect_color_pastille(subjects_area, cell)
            
            # Si c'est une nouvelle matière
            if text.strip() and not any(c.isdigit() for c in text):
                if current_subject:
                    subjects_info.append(current_subject)
                current_subject = parse_subject_info(text)
                current_subject['pastille'] = pastille
            # Sinon, ajouter les informations à la matière courante
            elif current_subject:
                # TODO: Implémenter la logique pour ajouter les notes et l'appréciation
                pass
        
        # Ajouter la dernière matière
        if current_subject:
            subjects_info.append(current_subject)
        
        return subjects_info

    def _extract_footer_info(self, image):
        """Extrait les informations du pied de page."""
        # Découpage de la zone de pied de page
        footer_height = int(image.shape[0] * 0.1)
        footer = image[-footer_height:, :]
        
        # Détection des lignes du tableau
        horizontal_lines, vertical_lines = detect_table_lines(footer)
        
        # Trouver les cellules
        cells = find_cell_boundaries(horizontal_lines, vertical_lines)
        
        # Extraire le texte de chaque cellule
        footer_text = ""
        for cell in cells:
            text = extract_text_from_cell(footer, cell)
            footer_text += text + "\n"
        
        # Parser les informations
        return parse_footer_text(footer_text)

    def process_image(self, image_path):
        """Traite une image de bulletin et extrait toutes les informations."""
        try:
            # Lecture de l'image
            if image_path.lower().endswith('.pdf'):
                images = convert_from_path(image_path)
                image = np.array(images[0])
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            else:
                image = cv2.imread(image_path)
            
            if image is None:
                raise ValueError(f"Impossible de lire l'image : {image_path}")
            
            # Prétraitement
            processed_image = self._preprocess_image(image)
            
            # Extraction des informations
            header_info = self._extract_header_info(processed_image)
            subjects_info = self._extract_subject_info(processed_image)
            footer_info = self._extract_footer_info(processed_image)
            
            # Création du DataFrame
            data = []
            for subject in subjects_info:
                row = {**header_info, **subject, **footer_info}
                data.append(row)
            
            return pd.DataFrame(data)
            
        except Exception as e:
            logger.error(f"Erreur lors du traitement de {image_path}: {str(e)}")
            raise

    def process_multiple_files(self, file_paths):
        """Traite plusieurs fichiers et combine les résultats."""
        all_data = []
        for file_path in file_paths:
            try:
                df = self.process_image(file_path)
                all_data.append(df)
            except Exception as e:
                logger.error(f"Erreur avec le fichier {file_path}: {str(e)}")
                continue
        
        if not all_data:
            raise ValueError("Aucune donnée n'a pu être extraite des fichiers")
        
        return pd.concat(all_data, ignore_index=True) 