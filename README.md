# Extracteur de Bulletins Scolaires

Cet outil permet d'extraire automatiquement les informations des bulletins scolaires (images ou PDF) et de les exporter au format CSV.

## Fonctionnalités

- Extraction automatique des informations des bulletins scolaires
- Support des formats PNG, JPEG et PDF
- Interface utilisateur intuitive avec Streamlit
- Export des données au format CSV
- Prévisualisation des données avant export
- Gestion des erreurs d'extraction

## Prérequis

- Python 3.8 ou supérieur
- Tesseract OCR (pour le support OCR)

## Installation

1. Cloner le repository :
```bash
git clone [URL_DU_REPO]
cd [NOM_DU_REPO]
```

2. Créer un environnement virtuel :
```bash
python -m venv venv
source venv/bin/activate  # Sur Windows : venv\Scripts\activate
```

3. Installer les dépendances :
```bash
pip install -r requirements.txt
```

## Utilisation

1. Lancer l'application :
```bash
streamlit run app.py
```

2. Dans l'interface web :
   - Sélectionner un ou plusieurs fichiers (PNG, JPEG, PDF)
   - Vérifier les données extraites
   - Corriger si nécessaire
   - Exporter au format CSV

## Structure du CSV

Le fichier CSV généré contient les colonnes suivantes :
- Nom
- Prénom
- Date de naissance
- Classe
- Professeur principal
- Matière
- Note T1
- Note T2
- Note T3
- Moyenne
- Position
- Appréciation
- Pastille/Code couleur
- Absences
- Retards
- Moyenne générale

## Contribution

Les contributions sont les bienvenues ! N'hésitez pas à ouvrir une issue ou une pull request. 